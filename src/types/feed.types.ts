// Feed页面相关类型定义
import { MuscleGroupEnum } from './muscle.types';

// 基础用户信息接口
export interface FeedUser {
  id: string;
  name: string;
  username: string;
  avatar: string;
  isVerified?: boolean;
}

// 训练动作接口
export interface WorkoutExercise {
  id: string;
  name: string;
  image_url?: string;
  sets: number;
  reps: number;
  weight: number; // kg
  rest_time?: number; // seconds
  primary_muscles: MuscleGroupEnum[];
  secondary_muscles?: MuscleGroupEnum[];
  notes?: string;
}

// 肌肉群强度计算结果
export interface MuscleGroupIntensity {
  muscle: MuscleGroupEnum;
  totalWeight: number;
  percentage: number;
  colorIntensity: 'light' | 'medium' | 'heavy'; // 对应--accent-300, --accent-400, --accent-500
  isPrimary: boolean;
}

// 训练数据接口
export interface WorkoutData {
  id: string;
  name: string;
  duration_seconds: number;
  total_sets: number;
  total_volume: number; // kg
  calories_burned?: number;
  exercises: WorkoutExercise[];
  muscle_intensities?: MuscleGroupIntensity[];
  created_at: string;
}

// 轮播图项目接口
export interface CarouselItem {
  id: string;
  type: 'muscle_illustration' | 'user_image';
  content: {
    // 肌肉示意图内容
    muscle_data?: {
      selectedMuscles: MuscleGroupEnum[];
      muscleColorConfig: { [key: string]: 'primary' | 'secondary' };
      intensities: MuscleGroupIntensity[];
    };
    // 用户图像内容
    image_data?: {
      url: string;
      alt: string;
      caption?: string;
    };
  };
}

// 动态帖子内容接口
export interface FeedPostContent {
  text?: string;
  workout?: WorkoutData;
  images?: string[];
  carousel_items?: CarouselItem[];
}

// 动态帖子统计接口
export interface FeedPostStats {
  likes: number;
  comments: number;
  shares: number;
}

// 扩展的动态帖子接口
export interface FeedPost {
  id: string;
  user: FeedUser;
  content: FeedPostContent;
  stats: FeedPostStats;
  isLiked: boolean;
  timestamp: Date;
  location?: string;
  visibility: 'everyone' | 'friends' | 'private';
  tags?: string[];
}

// 动态筛选类型
export type FeedFilterType = 'all' | 'workouts' | 'following';

// 动态页面状态接口
export interface FeedPageState {
  posts: FeedPost[];
  filter: FeedFilterType;
  loading: boolean;
  error: string | null;
  hasMore: boolean;
  page: number;
}

// API响应接口
export interface FeedPostsResponse {
  posts: FeedPost[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

// 创建帖子请求接口
export interface CreatePostRequest {
  title?: string;
  content: string;
  related_workout_id?: string;
  image_urls?: string[];
  visibility: 'everyone' | 'friends' | 'private';
  tags?: string[];
}

// 分享训练请求接口
export interface ShareWorkoutRequest {
  title: string;
  content: string;
  workout_data: WorkoutData;
  images?: string[];
  visibility: 'everyone' | 'friends' | 'private';
  tags?: string[];
}

// 评论接口
export interface FeedComment {
  id: string;
  post_id: string;
  user: FeedUser;
  content: string;
  likes: number;
  isLiked: boolean;
  created_at: string;
  replies?: FeedComment[];
}

// 通知接口
export interface FeedNotification {
  id: string;
  type: 'like' | 'comment' | 'follow' | 'workout_share';
  from_user: FeedUser;
  post?: FeedPost;
  message: string;
  is_read: boolean;
  created_at: string;
}

// 用户统计接口
export interface UserStats {
  total_workouts: number;
  total_volume: number;
  followers_count: number;
  following_count: number;
  posts_count: number;
}

// 关注状态接口
export interface FollowStatus {
  is_following: boolean;
  is_followed_by: boolean;
}

// 肌肉颜色配置类型
export interface MuscleColorConfig {
  [muscleGroup: string]: {
    intensity: 'light' | 'medium' | 'heavy';
    type: 'primary' | 'secondary';
  };
}

// 训练动作列表组件属性
export interface ExerciseListProps {
  exercises: WorkoutExercise[];
  maxVisible?: number;
  showImages?: boolean;
  onViewMore?: () => void;
  className?: string;
}

// 轮播图组件属性
export interface WorkoutCarouselProps {
  items: CarouselItem[];
  autoPlay?: boolean;
  showIndicators?: boolean;
  className?: string;
  onItemChange?: (index: number) => void;
}

// 肌肉示意图卡片属性
export interface MuscleIllustrationCardProps {
  muscleIntensities: MuscleGroupIntensity[];
  theme?: 'light' | 'dark';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}
