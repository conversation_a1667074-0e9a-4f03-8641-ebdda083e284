// Feed页面相关类型定义
import { MuscleGroupEnum } from './muscle.types';

// ===== API数据类型定义（基于真实后端数据） =====

// API用户信息接口（后端返回格式）
export interface ApiUser {
  id: number;
  nickname: string;
  avatar_url: string;
  gender: number; // 0:未知, 1:男, 2:女
  age?: number | null;
  weight?: number | null;
  height?: number | null;
  activity_level: number;
  bmi?: number | null;
  tedd?: any | null;
  completed: boolean;
  country?: string | null;
  province?: string | null;
  city?: string | null;
  experience_level?: string | null;
  fitness_goal?: string | null;
  health_conditions?: string | null;
  allergies?: string | null;
  created_at?: string | null;
  birthday?: string | null;
}

// API训练组记录接口
export interface ApiSetRecord {
  id: number;
  workout_exercise_id: number;
  set_number: number;
  set_type: 'normal' | 'warmup' | 'dropset' | 'failure';
  weight: number;
  reps: number;
  completed: boolean;
  notes: string;
  created_at: string;
  updated_at: string;
}

// API训练动作接口
export interface ApiWorkoutExercise {
  id: number;
  workout_id: number;
  exercise_id: number;
  exercise_name: string;
  exercise_image: string;
  exercise_description?: string | null;
  video_url: string;
  sets: number;
  reps: string; // 可能是 "12" 或 "12,12,12,12"
  rest_seconds: number;
  order: number;
  notes?: string | null;
  exercise_type: 'weight_reps' | 'time' | 'distance';
  superset_group?: string | null;
  weight?: number | null;
  set_records: ApiSetRecord[];
}

// API训练详情接口
export interface ApiWorkoutDetail {
  id: number;
  name: string;
  training_plan_id: number;
  day_of_week?: number | null;
  day_number: number;
  description?: string | null;
  estimated_duration?: number | null;
  scheduled_date?: string | null;
  status: 'completed' | 'in_progress' | 'planned';
  actual_duration: number;
  net_duration?: number | null;
  start_time: string;
  end_time: string;
  last_status_change?: string | null;
  created_at: string;
  updated_at: string;
  workout_exercises: ApiWorkoutExercise[];
}

// API训练基本信息接口
export interface ApiWorkout {
  id: number;
  name: string;
  title: string;
  date: string;
}

// API帖子接口（后端返回格式）
export interface ApiFeedPost {
  id: number;
  title: string;
  content: string;
  image_urls?: string[] | null;
  related_workout_id?: number | null;
  user_id: number;
  like_count: number;
  comment_count: number;
  share_count: number;
  status: 'ACTIVE' | 'DELETED' | 'HIDDEN';
  reported_count: number;
  view_count: number;
  created_at: string;
  updated_at: string;
  user: ApiUser;
  is_liked_by_current_user: boolean;
  related_workout?: ApiWorkout | null;
  related_workout_detail?: ApiWorkoutDetail | null;
  images: string[]; // 图片URL数组
  comments_summary: any[]; // 评论摘要
  tags: string[]; // 标签数组
}

// API分页响应接口
export interface ApiFeedResponse {
  total: number;
  items: ApiFeedPost[];
}

// ===== 前端使用的类型定义（转换后格式） =====

// 基础用户信息接口（前端使用）
export interface FeedUser {
  id: string;
  name: string;
  username: string;
  avatar: string;
  isVerified?: boolean;
}

// 训练组记录接口（前端使用）
export interface SetRecord {
  id: string;
  setNumber: number;
  setType: 'normal' | 'warmup' | 'dropset' | 'failure';
  weight: number;
  reps: number;
  completed: boolean;
  notes?: string;
}

// 训练动作接口（前端使用）
export interface WorkoutExercise {
  id: string;
  name: string;
  image_url?: string;
  sets: number;
  reps: number | string; // 支持单个数字或逗号分隔的字符串
  weight: number; // kg
  rest_time?: number; // seconds
  primary_muscles: MuscleGroupEnum[];
  secondary_muscles?: MuscleGroupEnum[];
  notes?: string;
  exercise_type?: 'weight_reps' | 'time' | 'distance';
  video_url?: string;
  set_records?: SetRecord[]; // 详细的组记录
  order?: number; // 动作顺序
}

// 肌肉群强度计算结果
export interface MuscleGroupIntensity {
  muscle: MuscleGroupEnum;
  totalWeight: number;
  percentage: number;
  colorIntensity: 'light' | 'medium' | 'heavy'; // 对应--accent-300, --accent-400, --accent-500
  isPrimary: boolean;
}

// 训练数据接口（前端使用）
export interface WorkoutData {
  id: string;
  name: string;
  duration_seconds: number;
  total_sets: number;
  total_volume: number; // kg
  calories_burned?: number;
  exercises: WorkoutExercise[];
  muscle_intensities?: MuscleGroupIntensity[];
  created_at: string;
  status?: 'completed' | 'in_progress' | 'planned';
  start_time?: string;
  end_time?: string;
}

// 轮播图项目接口
export interface CarouselItem {
  id: string;
  type: 'muscle_illustration' | 'user_image';
  content: {
    // 肌肉示意图内容
    muscle_data?: {
      selectedMuscles: MuscleGroupEnum[];
      muscleColorConfig: { [key: string]: 'primary' | 'secondary' };
      intensities: MuscleGroupIntensity[];
    };
    // 用户图像内容
    image_data?: {
      url: string;
      alt: string;
      caption?: string;
    };
  };
}

// 动态帖子内容接口
export interface FeedPostContent {
  text?: string;
  workout?: WorkoutData;
  images?: string[];
  carousel_items?: CarouselItem[];
}

// 动态帖子统计接口（前端使用）
export interface FeedPostStats {
  likes: number;
  comments: number;
  shares: number;
  views?: number; // 新增浏览数
  reports?: number; // 新增举报数
}

// 扩展的动态帖子接口（前端使用）
export interface FeedPost {
  id: string;
  title?: string; // 新增标题字段
  user: FeedUser;
  content: FeedPostContent;
  stats: FeedPostStats;
  isLiked: boolean;
  timestamp: Date;
  location?: string;
  visibility: 'everyone' | 'friends' | 'private';
  tags: string[]; // 改为必需字段
  status?: 'ACTIVE' | 'DELETED' | 'HIDDEN'; // 新增状态字段
}

// 动态筛选类型
export type FeedFilterType = 'all' | 'workouts' | 'following';

// 动态页面状态接口
export interface FeedPageState {
  posts: FeedPost[];
  filter: FeedFilterType;
  loading: boolean;
  error: string | null;
  hasMore: boolean;
  page: number;
}

// API响应接口（前端使用）
export interface FeedPostsResponse {
  posts: FeedPost[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

// 数据验证结果接口
export interface DataValidationResult {
  isValid: boolean;
  missingFields: string[];
  warnings: string[];
  errors?: string[];
}

// 创建帖子请求接口
export interface CreatePostRequest {
  title?: string;
  content: string;
  related_workout_id?: string;
  image_urls?: string[];
  visibility: 'everyone' | 'friends' | 'private';
  tags?: string[];
}

// 分享训练请求接口
export interface ShareWorkoutRequest {
  title: string;
  content: string;
  workout_data: WorkoutData;
  images?: string[];
  visibility: 'everyone' | 'friends' | 'private';
  tags?: string[];
}

// 评论接口
export interface FeedComment {
  id: string;
  post_id: string;
  user: FeedUser;
  content: string;
  likes: number;
  isLiked: boolean;
  created_at: string;
  replies?: FeedComment[];
}

// 通知接口
export interface FeedNotification {
  id: string;
  type: 'like' | 'comment' | 'follow' | 'workout_share';
  from_user: FeedUser;
  post?: FeedPost;
  message: string;
  is_read: boolean;
  created_at: string;
}

// 用户统计接口
export interface UserStats {
  total_workouts: number;
  total_volume: number;
  followers_count: number;
  following_count: number;
  posts_count: number;
}

// 关注状态接口
export interface FollowStatus {
  is_following: boolean;
  is_followed_by: boolean;
}

// 肌肉颜色配置类型
export interface MuscleColorConfig {
  [muscleGroup: string]: {
    intensity: 'light' | 'medium' | 'heavy';
    type: 'primary' | 'secondary';
  };
}

// 训练动作列表组件属性
export interface ExerciseListProps {
  exercises: WorkoutExercise[];
  maxVisible?: number;
  showImages?: boolean;
  onViewMore?: () => void;
  className?: string;
}

// 轮播图组件属性
export interface WorkoutCarouselProps {
  items: CarouselItem[];
  autoPlay?: boolean;
  showIndicators?: boolean;
  className?: string;
  onItemChange?: (index: number) => void;
}

// 肌肉示意图卡片属性
export interface MuscleIllustrationCardProps {
  muscleIntensities: MuscleGroupIntensity[];
  theme?: 'light' | 'dark';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}
