// 社区功能API服务
import {
  FeedPost,
  FeedPostsResponse,
  CreatePostRequest,
  ShareWorkoutRequest,
  FeedComment,
  FeedNotification,
  UserStats,
  FollowStatus,
  WorkoutData
} from '../types/feed.types';
import { apiClient } from '../utils/apiClient';
import { authService } from './authService';

// 基础HTTP请求方法类型
type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE';

// 请求配置接口
interface RequestConfig {
  method: HttpMethod;
  headers?: Record<string, string>;
  body?: any;
}

// 基础请求函数
const request = async <T>(url: string, config: RequestConfig = { method: 'GET' }): Promise<T> => {
  const { method, headers = {}, body } = config;
  
  // 设置默认headers
  const defaultHeaders: Record<string, string> = {
    'Content-Type': 'application/json',
    ...headers
  };

  // 获取认证token（如果存在）
  const token = localStorage.getItem('auth_token');
  if (token) {
    defaultHeaders['Authorization'] = `Bearer ${token}`;
  }

  const requestConfig: RequestInit = {
    method,
    headers: defaultHeaders,
  };

  if (body && method !== 'GET') {
    requestConfig.body = JSON.stringify(body);
  }

  try {
    const response = await fetch(url, requestConfig);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error(`API request failed: ${method} ${url}`, error);
    throw error;
  }
};

// HTTP方法封装
const get = <T>(url: string, params?: Record<string, any>): Promise<T> => {
  const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
  return request<T>(`${url}${queryString}`, { method: 'GET' });
};

const post = <T>(url: string, data?: any): Promise<T> => {
  return request<T>(url, { method: 'POST', body: data });
};

const put = <T>(url: string, data?: any): Promise<T> => {
  return request<T>(url, { method: 'PUT', body: data });
};

const del = <T>(url: string): Promise<T> => {
  return request<T>(url, { method: 'DELETE' });
};

/**
 * 社区API服务类
 */
export class CommunityService {
  private static readonly BASE_PATH = '/api/v1/community';

  // ===== 帖子相关 =====

  /**
   * 获取帖子列表（使用真实API）
   */
  static async getPosts(params: { skip?: number; limit?: number } = {}): Promise<any> {
    console.log('【社区服务】开始获取帖子列表:', params);

    try {
      // 确保用户已认证
      if (!authService.isAuthenticated()) {
        console.log('【社区服务】用户未认证，开始登录');
        await authService.loginWithTestUser();
      }

      const { skip = 0, limit = 20 } = params;

      console.log('【社区服务】发起API请求:', {
        endpoint: `${this.BASE_PATH}/posts/`,
        params: { skip, limit }
      });

      // 使用apiClient发起请求
      const response = await apiClient.get(`${this.BASE_PATH}/posts/`, { skip, limit });

      console.log('【社区服务】获取帖子列表成功:', {
        dataType: typeof response,
        isArray: Array.isArray(response),
        length: Array.isArray(response) ? response.length : 'N/A',
        firstItem: Array.isArray(response) && response.length > 0 ? response[0] : null
      });

      return response;

    } catch (error) {
      console.error('【社区服务】获取帖子列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取帖子详情
   */
  static async getPostDetail(postId: string): Promise<FeedPost> {
    try {
      return await get<FeedPost>(`${this.BASE_URL}/posts/${postId}`);
    } catch (error) {
      console.error('获取帖子详情失败:', error);
      throw error;
    }
  }

  /**
   * 创建帖子
   */
  static async createPost(postData: CreatePostRequest): Promise<FeedPost> {
    return post<FeedPost>(`${this.BASE_URL}/posts/`, postData);
  }

  /**
   * 更新帖子
   */
  static async updatePost(postId: string, postData: Partial<CreatePostRequest>): Promise<FeedPost> {
    return put<FeedPost>(`${this.BASE_URL}/posts/${postId}`, postData);
  }

  /**
   * 点赞帖子切换
   */
  static async togglePostLike(postId: string): Promise<{ liked: boolean; likes_count: number }> {
    return post<{ liked: boolean; likes_count: number }>(`${this.BASE_URL}/posts/${postId}/like/`);
  }

  /**
   * 举报帖子
   */
  static async reportPost(postId: string, reason: string): Promise<{ success: boolean }> {
    return post<{ success: boolean }>(`${this.BASE_URL}/posts/${postId}/report/`, { reason });
  }

  // ===== 评论相关 =====

  /**
   * 获取帖子的评论列表
   */
  static async getPostComments(
    postId: string, 
    params: { skip?: number; limit?: number } = {}
  ): Promise<{ comments: FeedComment[]; total: number }> {
    const { skip = 0, limit = 20 } = params;
    return get<{ comments: FeedComment[]; total: number }>(
      `${this.BASE_URL}/posts/${postId}/comments/`, 
      { skip, limit }
    );
  }

  /**
   * 创建评论
   */
  static async createComment(postId: string, content: string): Promise<FeedComment> {
    try {
      return await post<FeedComment>(`${this.BASE_URL}/posts/${postId}/comments/`, { content });
    } catch (error) {
      console.error('创建评论失败:', error);
      throw error;
    }
  }

  /**
   * 回复评论
   */
  static async replyComment(commentId: string, content: string): Promise<FeedComment> {
    try {
      return await post<FeedComment>(`${this.BASE_URL}/comments/${commentId}/replies/`, { content });
    } catch (error) {
      console.error('回复评论失败:', error);
      throw error;
    }
  }

  /**
   * 点赞评论切换
   */
  static async toggleCommentLike(commentId: string): Promise<{ liked: boolean; likes_count: number }> {
    try {
      return await post<{ liked: boolean; likes_count: number }>(
        `${this.BASE_URL}/comments/${commentId}/like/`
      );
    } catch (error) {
      console.error('切换评论点赞失败:', error);
      throw error;
    }
  }

  // ===== 通知相关 =====

  /**
   * 获取通知列表
   */
  static async getNotifications(
    params: { skip?: number; limit?: number } = {}
  ): Promise<{ notifications: FeedNotification[]; total: number }> {
    const { skip = 0, limit = 20 } = params;
    return get<{ notifications: FeedNotification[]; total: number }>(
      `${this.BASE_URL}/notifications/`, 
      { skip, limit }
    );
  }

  /**
   * 标记通知为已读
   */
  static async markNotificationAsRead(notificationId: string): Promise<{ success: boolean }> {
    return put<{ success: boolean }>(`${this.BASE_URL}/notifications/${notificationId}/read/`);
  }

  /**
   * 标记所有通知为已读
   */
  static async markAllNotificationsAsRead(): Promise<{ success: boolean }> {
    return put<{ success: boolean }>(`${this.BASE_URL}/notifications/read-all/`);
  }

  // ===== 用户相关 =====

  /**
   * 获取用户信息
   */
  static async getUserProfile(userId: string): Promise<any> {
    return get<any>(`${this.BASE_URL}/users/${userId}`);
  }

  /**
   * 获取用户统计数据
   */
  static async getUserStats(userId: string): Promise<UserStats> {
    return get<UserStats>(`${this.BASE_URL}/users/${userId}/stats`);
  }

  /**
   * 关注用户切换
   */
  static async toggleFollowUser(userId: string): Promise<{ following: boolean; followers_count: number }> {
    try {
      const response = await post<{ following: boolean; followers_count: number }>(
        `${this.BASE_URL}/users/${userId}/follow/`
      );
      return response;
    } catch (error) {
      console.error('切换关注状态失败:', error);
      throw error;
    }
  }

  /**
   * 检查用户关注状态
   */
  static async checkFollowStatus(userId: string): Promise<FollowStatus> {
    try {
      return await get<FollowStatus>(`${this.BASE_URL}/users/${userId}/follow-status/`);
    } catch (error) {
      console.error('检查关注状态失败:', error);
      throw error;
    }
  }

  // ===== 训练相关 =====

  /**
   * 获取训练详情
   */
  static async getWorkoutDetail(workoutId: string): Promise<WorkoutData> {
    return get<WorkoutData>(`${this.BASE_URL}/daily-workouts/${workoutId}`);
  }

  /**
   * 创建训练记录
   */
  static async createWorkout(workoutData: Partial<WorkoutData>): Promise<WorkoutData> {
    return post<WorkoutData>(`${this.BASE_URL}/daily-workouts/`, workoutData);
  }

  /**
   * 分享训练记录到社区
   */
  static async shareWorkout(workoutId: string, shareData: ShareWorkoutRequest): Promise<FeedPost> {
    return post<FeedPost>(`${this.BASE_URL}/workout/${workoutId}/share`, shareData);
  }
}

// 导出默认实例
export const communityService = CommunityService;
