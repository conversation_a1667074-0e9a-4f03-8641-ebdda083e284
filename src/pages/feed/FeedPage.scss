// Feed Page Styles
.feed-page {
  max-width: 600px;
  margin: 0 auto;
  padding: 0;
}

// Feed Header
.feed-header {
  margin-bottom: var(--space-6);
  
  .feed-title {
    margin-bottom: var(--space-4);
    
    h1 {
      font-size: var(--text-2xl);
      font-weight: var(--font-bold);
      color: var(--text-primary);
      margin: 0 0 var(--space-2) 0;
    }
    
    p {
      color: var(--text-secondary);
      font-size: var(--text-base);
      margin: 0;
    }
  }
  
  .feed-filters {
    display: flex;
    gap: var(--space-2);
    padding: var(--space-1);
    background: var(--bg-surface);
    border: 1px solid var(--primary-500);
    border-radius: var(--radius-lg);
    
    .filter-btn {
      flex: 1;
      padding: var(--space-2) var(--space-4);
      border: none;
      border-radius: var(--radius-md);
      background: transparent;
      color: var(--text-secondary);
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
      cursor: pointer;
      transition: all var(--transition-normal) var(--ease-in-out);
      
      &:hover {
        background: var(--bg-hover);  /* 使用浅色悬停背景，保持文字可读性 */
        color: var(--text-primary);
      }
      
      &.active {
        background: var(--accent-500);
        color: var(--text-on-accent);  /* 现在已正确定义为白色 */
      }
    }
  }
}

// Create Post Card
.create-post-card {
  background: var(--card-bg);  /* 使用统一的卡片背景色 */
  border: 1px solid var(--card-border);  /* 使用统一的卡片边框色 */
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  margin-bottom: var(--space-6);
  
  .create-post-header {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    margin-bottom: var(--space-4);
    
    .user-avatar {
      width: 2.5rem;
      height: 2.5rem;
      border-radius: var(--radius-full);
      overflow: hidden;
      border: 2px solid var(--accent-500);
      
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    
    .create-post-input {
      flex: 1;
      padding: var(--space-3) var(--space-4);
      background: var(--primary-600);
      border: 1px solid var(--primary-500);
      border-radius: var(--radius-full);
      color: var(--text-secondary);
      font-size: var(--text-base);
      cursor: pointer;
      transition: all var(--transition-normal) var(--ease-in-out);
      text-align: left;
      
      &:hover {
        background: var(--primary-500);
        border-color: var(--accent-500);
      }
    }
  }
  
  .create-post-actions {
    display: flex;
    gap: var(--space-3);
    
    .post-action-btn {
      display: flex;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-2) var(--space-3);
      background: transparent;
      border: 1px solid var(--primary-500);
      border-radius: var(--radius-md);
      color: var(--text-secondary);
      font-size: var(--text-sm);
      cursor: pointer;
      transition: all var(--transition-normal) var(--ease-in-out);
      
      svg {
        width: 1rem;
        height: 1rem;
        stroke-width: 2;
      }
      
      &:hover {
        background: var(--primary-600);
        color: var(--text-primary);
        border-color: var(--accent-500);
      }
    }
  }
}

// Feed Posts
.feed-posts {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

// Individual Feed Post
.feed-post {
  background: var(--card-bg);  /* 使用统一的卡片背景色 */
  border: 1px solid var(--card-border);  /* 使用统一的卡片边框色 */
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  transition: all var(--transition-normal) var(--ease-in-out);
  
  &:hover {
    border-color: var(--accent-500);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.1);
  }
}

// Post Header
.post-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-4);
  
  .post-user {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    
    .user-avatar {
      width: 2.5rem;
      height: 2.5rem;
      border-radius: var(--radius-full);
      overflow: hidden;
      border: 2px solid var(--accent-500);
      
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    
    .user-info {
      .user-name {
        display: flex;
        align-items: center;
        gap: var(--space-1);
        font-size: var(--text-base);
        font-weight: var(--font-semibold);
        color: var(--text-primary);
        margin-bottom: var(--space-1);
        
        .verified-icon {
          width: 1rem;
          height: 1rem;
          color: var(--accent-500);
        }
      }
      
      .post-meta {
        display: flex;
        align-items: center;
        gap: var(--space-2);
        font-size: var(--text-sm);
        color: var(--text-tertiary);
        
        .username {
          color: var(--accent-500);
        }
        
        .location {
          display: flex;
          align-items: center;
          gap: var(--space-1);
          
          svg {
            width: 0.875rem;
            height: 0.875rem;
            stroke-width: 2;
          }
        }
      }
    }
  }
  
  .post-menu-btn {
    padding: var(--space-1);
    background: transparent;
    border: none;
    border-radius: var(--radius-md);
    color: var(--text-tertiary);
    cursor: pointer;
    transition: all var(--transition-normal) var(--ease-in-out);
    
    svg {
      width: 1.25rem;
      height: 1.25rem;
      stroke-width: 2;
    }
    
    &:hover {
      background: var(--primary-600);
      color: var(--text-primary);
    }
  }
}

// Post Content
.post-content {
  margin-bottom: var(--space-4);
  
  .post-text {
    font-size: var(--text-base);
    line-height: var(--leading-relaxed);
    color: var(--text-primary);
    margin: 0 0 var(--space-4) 0;
  }
  
  .post-image {
    margin-bottom: var(--space-4);
    border-radius: var(--radius-lg);
    overflow: hidden;
    
    img {
      width: 100%;
      height: auto;
      display: block;
    }
  }
}

// Workout Summary
.workout-summary {
  background: var(--card-bg);  /* 使用统一的卡片背景色 */
  border: 1px solid var(--card-border);  /* 使用统一的卡片边框色 */
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  margin-bottom: var(--space-4);
  
  .workout-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-3);
    
    .workout-name {
      font-size: var(--text-lg);
      font-weight: var(--font-semibold);
      color: var(--text-primary);
      margin: 0;
    }
    
    .workout-badge {
      background: var(--accent-500);
      color: var(--text-on-accent);
      font-size: var(--text-xs);
      font-weight: var(--font-medium);
      padding: 0.25rem 0.75rem;
      border-radius: var(--radius-full);
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }
  }
  
  .workout-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-4);
    margin-bottom: var(--space-4);
    
    .workout-stat {
      text-align: center;
      
      .stat-value {
        display: block;
        font-size: var(--text-xl);
        font-weight: var(--font-bold);
        color: var(--text-primary);
        font-family: var(--font-mono);
        margin-bottom: var(--space-1);
      }
      
      .stat-label {
        font-size: var(--text-xs);
        color: var(--text-secondary);
        text-transform: uppercase;
        letter-spacing: 0.05em;
        font-weight: var(--font-medium);
      }
    }
  }
  
  .workout-exercises {
    font-size: var(--text-sm);
    
    .exercises-label {
      color: var(--text-secondary);
      font-weight: var(--font-medium);
    }
    
    .exercises-list {
      color: var(--text-primary);
      margin-left: var(--space-2);
    }
  }
}

// Post Actions
.post-actions {
  display: flex;
  justify-content: space-around;
  padding-top: var(--space-4);
  border-top: 1px solid var(--primary-500);
  
  .action-btn {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-3);
    background: transparent;
    border: none;
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    cursor: pointer;
    transition: all var(--transition-normal) var(--ease-in-out);
    
    svg {
      width: 1.125rem;
      height: 1.125rem;
      stroke-width: 2;
    }
    
    &:hover {
      background: var(--primary-600);
      color: var(--text-primary);
      transform: translateY(-1px);
    }
    
    &.like-btn {
      &.liked {
        color: var(--error-500);
        
        svg {
          fill: currentColor;
        }
      }
      
      &:hover {
        color: var(--error-500);
      }
    }
    
    &.comment-btn:hover {
      color: var(--accent-500);
    }
    
    &.share-btn:hover {
      color: var(--success-500);
    }
  }
}

// Load More
.load-more {
  display: flex;
  justify-content: center;
  margin: var(--space-8) 0;
  
  .load-more-btn {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-6);
    background: var(--bg-surface);
    border: 1px solid var(--primary-500);
    border-radius: var(--radius-lg);
    color: var(--text-secondary);
    font-size: var(--text-base);
    font-weight: var(--font-medium);
    cursor: pointer;
    transition: all var(--transition-normal) var(--ease-in-out);
    
    svg {
      width: 1.25rem;
      height: 1.25rem;
      stroke-width: 2;
    }
    
    &:hover {
      background: var(--primary-600);
      color: var(--text-primary);
      border-color: var(--accent-500);
      transform: translateY(-2px);
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .feed-page {
    padding: 0 var(--space-4);
  }
  
  .feed-header {
    .feed-title h1 {
      font-size: var(--text-xl);
    }
    
    .feed-filters {
      .filter-btn {
        padding: var(--space-2);
        font-size: var(--text-xs);
      }
    }
  }
  
  .create-post-card,
  .feed-post {
    padding: var(--space-4);
  }
  
  .post-header {
    .post-user {
      .user-avatar {
        width: 2rem;
        height: 2rem;
      }
      
      .user-info .user-name {
        font-size: var(--text-sm);
      }
    }
  }
  
  .workout-summary {
    .workout-stats {
      grid-template-columns: repeat(3, 1fr);
      gap: var(--space-2);
      
      .workout-stat .stat-value {
        font-size: var(--text-lg);
      }
    }
  }
  
  .post-actions {
    .action-btn {
      padding: var(--space-2);
      font-size: var(--text-xs);
      
      svg {
        width: 1rem;
        height: 1rem;
      }
    }
  }
  
  .create-post-actions {
    flex-direction: column;
    
    .post-action-btn {
      justify-content: center;
    }
  }
}

// Loading States
.feed-post.loading {
  .post-content,
  .post-actions {
    opacity: 0.7;
    pointer-events: none;
  }
}

// Animation for new posts
@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-1rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.feed-post.new-post {
  animation: slideInFromTop 0.3s ease-out;
}

// Focus states for accessibility
.filter-btn:focus,
.post-action-btn:focus,
.action-btn:focus,
.load-more-btn:focus,
.create-post-input:focus {
  outline: 2px solid var(--accent-500);
  outline-offset: 2px;
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  .feed-post,
  .action-btn,
  .load-more-btn {
    transition: none;
  }
  
  .feed-post:hover,
  .action-btn:hover,
  .load-more-btn:hover {
    transform: none;
  }
  
  .feed-post.new-post {
    animation: none;
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .feed-post,
  .create-post-card {
    border-width: 2px;
  }

  .workout-summary,
  .workout-post-content {
    border-width: 2px;
  }
}

// === 新的训练帖子内容样式 ===
.workout-post-content {
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  margin-top: var(--space-4);

  .workout-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-4);

    .workout-name {
      font-size: var(--text-lg);
      font-weight: var(--font-semibold);
      color: var(--text-primary);
      margin: 0;
    }

    .workout-badge {
      background: var(--accent-500);
      color: var(--text-on-accent);
      padding: var(--space-1) var(--space-3);
      border-radius: var(--radius-full);
      font-size: var(--text-xs);
      font-weight: var(--font-medium);
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }
  }

  // 训练统计摘要
  .workout-stats-summary {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--space-3);
    margin-bottom: var(--space-4);
    padding: var(--space-3);
    background: var(--bg-tertiary);
    border-radius: var(--radius-md);

    .workout-stat {
      text-align: center;

      .stat-value {
        display: block;
        font-size: var(--text-lg);
        font-weight: var(--font-bold);
        color: var(--accent-500);
        line-height: 1.2;
        font-family: var(--font-mono);
      }

      .stat-label {
        font-size: var(--text-xs);
        color: var(--text-secondary);
        margin-top: var(--space-1);
        text-transform: uppercase;
        letter-spacing: 0.05em;
        font-weight: var(--font-medium);
      }
    }
  }

  // 主要内容区域：左右布局
  .workout-main-content {
    display: flex;
    gap: var(--space-4);
    align-items: flex-start;

    // 左侧：训练动作列表（40%宽度）
    .workout-exercises-section {
      flex: 0 0 40%;
      min-width: 0; // 防止内容溢出

      .workout-exercise-list {
        // 继承ExerciseList组件的样式
        background: transparent;

        .exercise-summary {
          background: var(--bg-primary);
          border: 1px solid var(--border-light);
        }
      }
    }

    // 右侧：轮播图（60%宽度）
    .workout-carousel-section {
      flex: 1;
      min-height: 300px;

      .workout-post-carousel {
        height: 100%;
        border-radius: var(--radius-md);
        overflow: hidden;
        box-shadow: var(--shadow-sm);
        background: var(--bg-primary);
      }
    }
  }
}

// === 响应式设计：训练帖子内容 ===
@media (max-width: 768px) {
  .workout-post-content {
    padding: var(--space-3);

    .workout-stats-summary {
      grid-template-columns: repeat(2, 1fr);
      gap: var(--space-2);
      padding: var(--space-2);

      .workout-stat {
        .stat-value {
          font-size: var(--text-base);
        }
      }
    }

    // 移动端改为上下布局
    .workout-main-content {
      flex-direction: column;
      gap: var(--space-3);

      .workout-exercises-section {
        flex: none;
        width: 100%;
      }

      .workout-carousel-section {
        flex: none;
        width: 100%;
        min-height: 250px;
      }
    }
  }
}

// === 极小屏幕优化 ===
@media (max-width: 480px) {
  .workout-post-content {
    .workout-stats-summary {
      grid-template-columns: repeat(2, 1fr);

      .workout-stat {
        .stat-value {
          font-size: var(--text-sm);
        }

        .stat-label {
          font-size: var(--text-xs);
        }
      }
    }

    .workout-carousel-section {
      min-height: 200px;
    }
  }
}

// === iPad横屏优化 ===
@media (min-width: 768px) and (max-width: 1024px) and (orientation: landscape) {
  .workout-post-content {
    .workout-main-content {
      .workout-exercises-section {
        flex: 0 0 35%;
      }

      .workout-carousel-section {
        flex: 1;
        min-height: 280px;
      }
    }
  }
}