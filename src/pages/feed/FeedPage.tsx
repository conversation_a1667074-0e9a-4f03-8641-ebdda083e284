import React, { useState, useMemo } from 'react';
import {
  FeedPost,
  FeedFilterType,
  WorkoutExercise,
  CarouselItem
} from '../../types/feed.types';
import { MuscleGroupEnum } from '../../types/muscle.types';
import {
  calculateMuscleIntensities,
  generateStaticMuscleColorConfig
} from '../../utils/muscleColorCalculator';
import { WorkoutCarousel } from '../../components/fitness/WorkoutCarousel/WorkoutCarousel';
import { ExerciseList } from '../../components/fitness/ExerciseList/ExerciseList';
import './FeedPage.scss';

// Mock训练动作数据
const mockExercises: WorkoutExercise[] = [
  {
    id: 'ex_1',
    name: '杠铃卧推',
    image_url: '/api/placeholder/48/48',
    sets: 4,
    reps: 8,
    weight: 80,
    primary_muscles: [MuscleGroupEnum.CHEST],
    secondary_muscles: [MuscleGroupEnum.TRICEPS, MuscleGroupEnum.SHOULDERS_FRONT]
  },
  {
    id: 'ex_2',
    name: '哑铃肩上推举',
    image_url: '/api/placeholder/48/48',
    sets: 3,
    reps: 10,
    weight: 25,
    primary_muscles: [MuscleGroupEnum.SHOULDERS_FRONT],
    secondary_muscles: [MuscleGroupEnum.TRICEPS]
  },
  {
    id: 'ex_3',
    name: '双杠臂屈伸',
    image_url: '/api/placeholder/48/48',
    sets: 3,
    reps: 12,
    weight: 0, // 自重
    primary_muscles: [MuscleGroupEnum.TRICEPS],
    secondary_muscles: [MuscleGroupEnum.CHEST, MuscleGroupEnum.SHOULDERS_FRONT]
  },
  {
    id: 'ex_4',
    name: '哑铃飞鸟',
    image_url: '/api/placeholder/48/48',
    sets: 3,
    reps: 12,
    weight: 15,
    primary_muscles: [MuscleGroupEnum.CHEST],
    secondary_muscles: []
  }
];

// 生成轮播图数据的辅助函数
const generateCarouselItems = (exercises: WorkoutExercise[], userImages?: string[]): CarouselItem[] => {
  const items: CarouselItem[] = [];

  // 计算肌肉强度
  const muscleIntensities = calculateMuscleIntensities(exercises);
  const selectedMuscles = muscleIntensities.map(intensity => intensity.muscle);
  const muscleColorConfig = generateStaticMuscleColorConfig(muscleIntensities);

  // 添加肌肉示意图项
  items.push({
    id: 'muscle_illustration',
    type: 'muscle_illustration',
    content: {
      muscle_data: {
        selectedMuscles,
        muscleColorConfig,
        intensities: muscleIntensities
      }
    }
  });

  // 添加用户图像项
  if (userImages && userImages.length > 0) {
    userImages.forEach((imageUrl, index) => {
      items.push({
        id: `user_image_${index}`,
        type: 'user_image',
        content: {
          image_data: {
            url: imageUrl,
            alt: `训练图片 ${index + 1}`,
            caption: index === 0 ? '训练现场' : undefined
          }
        }
      });
    });
  }

  return items;
};

// Mock Feed数据
const mockFeedData: FeedPost[] = [
  {
    id: 'post_1',
    user: {
      id: 'user_1',
      name: 'Mizuho Miyagawa',
      username: 'mizuho_fitness',
      avatar: '/api/placeholder/40/40',
      isVerified: true
    },
    content: {
      text: 'Friday Afternoon Workout',
      workout: {
        id: 'workout_1',
        name: 'Push Day - 胸肩三头',
        duration_seconds: 4500, // 75分钟
        total_sets: 13,
        total_volume: 2640, // 计算得出
        calories_burned: 320,
        exercises: mockExercises,
        created_at: '2025-01-17T08:30:00Z'
      },
      carousel_items: generateCarouselItems(mockExercises, ['/api/placeholder/400/300'])
    },
    stats: {
      likes: 24,
      comments: 8,
      shares: 3
    },
    isLiked: true,
    timestamp: new Date('2025-01-17T08:30:00'),
    location: '上海健身房',
    visibility: 'everyone',
    tags: ['胸部训练', 'Push Day']
  }
];

const FeedPage: React.FC = () => {
  const [posts, setPosts] = useState<FeedPost[]>(mockFeedData);
  const [filter, setFilter] = useState<FeedFilterType>('all');

  // 格式化时间戳
  const formatTimestamp = (date: Date): string => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 60) {
      return `${diffInMinutes}分钟前`;
    } else if (diffInMinutes < 1440) {
      return `${Math.floor(diffInMinutes / 60)}小时前`;
    } else {
      return date.toLocaleDateString('zh-CN', {
        month: 'short',
        day: 'numeric'
      });
    }
  };

  // 处理点赞
  const handleLike = async (postId: string) => {
    try {
      // 这里可以调用API
      // await communityService.togglePostLike(postId);

      setPosts(posts.map(post => {
        if (post.id === postId) {
          return {
            ...post,
            isLiked: !post.isLiked,
            stats: {
              ...post.stats,
              likes: post.isLiked ? post.stats.likes - 1 : post.stats.likes + 1
            }
          };
        }
        return post;
      }));
    } catch (error) {
      console.error('点赞失败:', error);
    }
  };

  // 过滤帖子
  const filteredPosts = useMemo(() => {
    return posts.filter(post => {
      switch (filter) {
        case 'workouts':
          return post.content.workout;
        case 'following':
          // 这里可以根据关注关系过滤
          return true;
        default:
          return true;
      }
    });
  }, [posts, filter]);

  return (
    <div className="feed-page">
      {/* Feed Header */}
      <div className="feed-header">
        <div className="feed-title">
  
          <p>发现健身伙伴的最新动态</p>
        </div>
        
        {/* Filter Tabs */}
        <div className="feed-filters">
          <button 
            className={`filter-btn ${filter === 'all' ? 'active' : ''}`}
            onClick={() => setFilter('all')}
          >
            全部动态
          </button>
          <button 
            className={`filter-btn ${filter === 'workouts' ? 'active' : ''}`}
            onClick={() => setFilter('workouts')}
          >
            训练记录
          </button>
          <button 
            className={`filter-btn ${filter === 'following' ? 'active' : ''}`}
            onClick={() => setFilter('following')}
          >
            关注的人
          </button>
        </div>
      </div>

      {/* Create Post */}
      <div className="create-post-card">
        <div className="create-post-header">
          <div className="user-avatar">
            <img src="/api/placeholder/40/40" alt="Your avatar" />
          </div>
          <button className="create-post-input">
            分享你的健身动态...
          </button>
        </div>
        <div className="create-post-actions">
          <button className="post-action-btn">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M15 10L11 14L17 20L21 4L3 11L9 13V19L12 16"/>
            </svg>
            分享训练
          </button>
          <button className="post-action-btn">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
              <circle cx="8.5" cy="8.5" r="1.5"/>
              <path d="M21 15L16 10L5 21"/>
            </svg>
            上传图片
          </button>
        </div>
      </div>

      {/* Feed Posts */}
      <div className="feed-posts">
        {filteredPosts.map(post => (
          <article key={post.id} className="feed-post">
            {/* Post Header */}
            <div className="post-header">
              <div className="post-user">
                <div className="user-avatar">
                  <img src={post.user.avatar} alt={post.user.name} />
                </div>
                <div className="user-info">
                  <div className="user-name">
                    {post.user.name}
                    {post.user.isVerified && (
                      <svg className="verified-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2L13.09 5.26L16 6L13.09 6.74L12 10L10.91 6.74L8 6L10.91 5.26L12 2Z"/>
                        <path d="M12 12L13.09 15.26L16 16L13.09 16.74L12 20L10.91 16.74L8 16L10.91 15.26L12 12Z"/>
                      </svg>
                    )}
                  </div>
                  <div className="post-meta">
                    <span className="username">@{post.user.username}</span>
                    <span className="timestamp">{formatTimestamp(post.timestamp)}</span>
                    {post.location && (
                      <span className="location">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                          <path d="M21 10C21 17 12 23 12 23S3 17 3 10C3 7.61305 3.94821 5.32387 5.63604 3.63604C7.32387 1.94821 9.61305 1 12 1C14.3869 1 16.6761 1.94821 18.3639 3.63604C20.0518 5.32387 21 7.61305 21 10Z"/>
                          <circle cx="12" cy="10" r="3"/>
                        </svg>
                        {post.location}
                      </span>
                    )}
                  </div>
                </div>
              </div>
              
              <button className="post-menu-btn" aria-label="更多选项">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <circle cx="12" cy="12" r="1"/>
                  <circle cx="19" cy="12" r="1"/>
                  <circle cx="5" cy="12" r="1"/>
                </svg>
              </button>
            </div>

            {/* Post Content */}
            <div className="post-content">
              {post.content.text && (
                <p className="post-text">{post.content.text}</p>
              )}
              
              {post.content.workout && (
                <div className="workout-post-content">
                  {/* 训练标题和统计 */}
                  <div className="workout-header">
                    <h4 className="workout-name">{post.content.workout.name}</h4>
                    <div className="workout-badge">训练记录</div>
                  </div>

                  <div className="workout-stats-summary">
                    <div className="workout-stat">
                      <span className="stat-value">{Math.round(post.content.workout.duration_seconds / 60)}</span>
                      <span className="stat-label">分钟</span>
                    </div>
                    <div className="workout-stat">
                      <span className="stat-value">{post.content.workout.total_volume}</span>
                      <span className="stat-label">kg</span>
                    </div>
                    <div className="workout-stat">
                      <span className="stat-value">{post.content.workout.calories_burned || 0}</span>
                      <span className="stat-label">卡路里</span>
                    </div>
                    <div className="workout-stat">
                      <span className="stat-value">{post.content.workout.exercises.reduce((sum, ex) => sum + ex.sets, 0)}</span>
                      <span className="stat-label">组数</span>
                    </div>
                  </div>

                  {/* 主要内容区域：左侧动作列表，右侧轮播图 */}
                  <div className="workout-main-content">
                    {/* 左侧：训练动作列表 */}
                    <div className="workout-exercises-section">
                      <ExerciseList
                        exercises={post.content.workout.exercises}
                        maxVisible={3}
                        showImages={true}
                        className="workout-exercise-list"
                      />
                    </div>

                    {/* 右侧：轮播图 */}
                    <div className="workout-carousel-section">
                      {post.content.carousel_items && post.content.carousel_items.length > 0 && (
                        <WorkoutCarousel
                          items={post.content.carousel_items}
                          showIndicators={true}
                          className="workout-post-carousel"
                        />
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Post Actions */}
            <div className="post-actions">
              <button 
                className={`action-btn like-btn ${post.isLiked ? 'liked' : ''}`}
                onClick={() => handleLike(post.id)}
              >
                <svg viewBox="0 0 24 24" fill={post.isLiked ? "currentColor" : "none"} stroke="currentColor">
                  <path d="M20.84 4.61C20.3292 4.099 19.7228 3.69364 19.0554 3.41708C18.3879 3.14052 17.6725 2.99817 16.95 2.99817C16.2275 2.99817 15.5121 3.14052 14.8446 3.41708C14.1772 3.69364 13.5708 4.099 13.06 4.61L12 5.67L10.94 4.61C9.9083 3.57831 8.50903 2.99871 7.05 2.99871C5.59096 2.99871 4.19169 3.57831 3.16 4.61C2.1283 5.64169 1.54871 7.04096 1.54871 8.5C1.54871 9.95904 2.1283 11.3583 3.16 12.39L12 21.23L20.84 12.39C21.351 11.8792 21.7563 11.2728 22.0329 10.6053C22.3095 9.93789 22.4518 9.22248 22.4518 8.5C22.4518 7.77752 22.3095 7.06211 22.0329 6.39468C21.7563 5.72725 21.351 5.12087 20.84 4.61Z"/>
                </svg>
                <span>{post.stats.likes}</span>
              </button>
              
              <button className="action-btn comment-btn">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path d="M21 15C21 15.5304 20.7893 16.0391 20.4142 16.4142C20.0391 16.7893 19.5304 17 19 17H7L3 21V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V15Z"/>
                </svg>
                <span>{post.stats.comments}</span>
              </button>
              
              <button className="action-btn share-btn">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <circle cx="18" cy="5" r="3"/>
                  <circle cx="6" cy="12" r="3"/>
                  <circle cx="18" cy="19" r="3"/>
                  <path d="M8.59 13.51L15.42 17.49"/>
                  <path d="M15.41 6.51L8.59 10.49"/>
                </svg>
                <span>{post.stats.shares}</span>
              </button>
            </div>
          </article>
        ))}
      </div>

      {/* Load More */}
      <div className="load-more">
        <button className="load-more-btn">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z"/>
            <circle cx="12" cy="12" r="3"/>
          </svg>
          查看更多动态
        </button>
      </div>
    </div>
  );
};

export default FeedPage; 