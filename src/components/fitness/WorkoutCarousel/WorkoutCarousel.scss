// WorkoutCarousel 组件样式
.workout-carousel {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: var(--radius-lg);
  background: var(--bg-surface);

  // 空状态
  &.empty {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    
    .empty-state {
      color: var(--text-tertiary);
      font-size: var(--text-sm);
    }
  }
}

// 轮播容器
.carousel-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

// 轮播轨道
.carousel-track {
  display: flex;
  width: 100%;
  height: 100%;
  will-change: transform;
  
  // iOS硬件加速优化
  transform: translateZ(0);
  backface-visibility: hidden;
}

// 轮播项
.carousel-item {
  flex: 0 0 100%;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &.active {
    z-index: 1;
  }
}

// 轮播项内容
.carousel-item-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-4);
}

// 肌肉示意图内容
.muscle-illustration-content {
  .muscle-illustration-wrapper {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    max-height: 300px;
    
    .carousel-muscle-illustration {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
    }
  }
  
  .muscle-stats {
    display: flex;
    gap: var(--space-6);
    margin-top: var(--space-4);
    padding: var(--space-3) var(--space-4);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    
    .muscle-count,
    .intensity-level {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: var(--space-1);
      
      .count,
      .level {
        font-size: var(--text-lg);
        font-weight: var(--font-semibold);
        color: var(--accent-500);
      }
      
      .label {
        font-size: var(--text-xs);
        color: var(--text-secondary);
      }
    }
  }
}

// 用户图像内容
.user-image-content {
  .user-image {
    width: 100%;
    height: auto;
    max-height: 300px;
    object-fit: cover;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-md);
  }
  
  .image-caption {
    margin-top: var(--space-3);
    padding: var(--space-2) var(--space-3);
    background: var(--bg-secondary);
    border-radius: var(--radius-sm);
    font-size: var(--text-sm);
    color: var(--text-secondary);
    text-align: center;
  }
}

// 导航按钮
.carousel-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: var(--ios-touch-target);
  height: var(--ios-touch-target);
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-normal) var(--ease-in-out);
  box-shadow: var(--shadow-md);
  z-index: 2;
  
  // iOS触摸优化
  -webkit-tap-highlight-color: transparent;
  
  &:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-50%) scale(1.05);
  }
  
  &:active {
    transform: translateY(-50%) scale(0.95);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: translateY(-50%);
  }
  
  &.prev {
    left: var(--space-3);
  }
  
  &.next {
    right: var(--space-3);
  }
  
  svg {
    width: 20px;
    height: 20px;
  }
}

// 指示器
.carousel-indicators {
  position: absolute;
  bottom: var(--space-3);
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: var(--space-2);
  z-index: 2;
  
  .indicator {
    width: 8px;
    height: 8px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all var(--transition-fast) var(--ease-in-out);
    
    // iOS触摸优化
    -webkit-tap-highlight-color: transparent;
    min-width: var(--ios-touch-target);
    min-height: var(--ios-touch-target);
    display: flex;
    align-items: center;
    justify-content: center;
    
    &::before {
      content: '';
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: currentColor;
      transition: all var(--transition-fast) var(--ease-in-out);
    }
    
    &.active {
      background: rgba(255, 255, 255, 0.9);
      
      &::before {
        background: var(--accent-500);
        transform: scale(1.2);
      }
    }
    
    &:hover:not(.active) {
      background: rgba(255, 255, 255, 0.7);
    }
  }
}

// 暗色主题适配
.theme-dark {
  .carousel-nav {
    background: rgba(30, 41, 59, 0.9);
    color: var(--text-primary);
    
    &:hover {
      background: rgba(30, 41, 59, 1);
    }
  }
  
  .carousel-indicators .indicator {
    background: rgba(30, 41, 59, 0.5);
    
    &.active {
      background: rgba(30, 41, 59, 0.9);
    }
    
    &:hover:not(.active) {
      background: rgba(30, 41, 59, 0.7);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .carousel-nav {
    width: 36px;
    height: 36px;
    
    svg {
      width: 16px;
      height: 16px;
    }
    
    &.prev {
      left: var(--space-2);
    }
    
    &.next {
      right: var(--space-2);
    }
  }
  
  .carousel-indicators {
    bottom: var(--space-2);
    
    .indicator {
      width: 6px;
      height: 6px;
      
      &::before {
        width: 6px;
        height: 6px;
      }
    }
  }
  
  .muscle-illustration-content {
    .muscle-illustration-wrapper {
      max-height: 250px;
    }
    
    .muscle-stats {
      gap: var(--space-4);
      margin-top: var(--space-3);
      padding: var(--space-2) var(--space-3);
    }
  }
  
  .user-image-content .user-image {
    max-height: 250px;
  }
}

// 减少动画支持
@media (prefers-reduced-motion: reduce) {
  .carousel-track {
    transition: none !important;
  }
  
  .carousel-nav,
  .carousel-indicators .indicator {
    transition: none;
  }
  
  .carousel-nav:hover,
  .carousel-nav:active {
    transform: translateY(-50%);
  }
}
