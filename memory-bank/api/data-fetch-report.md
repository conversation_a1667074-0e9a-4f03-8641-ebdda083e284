"# 动态数据获取测试报告\n\n## 测试时间\n2025-07-27T11:09:15.664Z\n\n## 认证结果\n- 状态: ❌ 失败\n- Token: 已获取\n- 用户: {\n  \"id\": 1,\n  \"email\": \"<EMAIL>\",\n  \"openid\": \"oCU0j7Rg9kzigLzquCBje3KfnQXk\",\n  \"nickName\": \"测试用户\"\n}\n- 错误: HTTP 404: {\"detail\":\"Not Found\"}\n\n## 数据获取结果\n- 状态: ✅ 成功\n- 数据类型: object\n- 是否为数组: false\n- 项目总数: 7\n\n## 数据统计\n- 包含训练数据: 0\n- 包含图片: 0\n- 包含评论: 0\n\n## 数据结构\n```json\n{\n  \"total\": \"number\",\n  \"items\": {\n    \"type\": \"array\",\n    \"length\": 7,\n    \"itemType\": \"object\"\n  }\n}\n```\n\n## 样例数据\n```json\n{\n  \"created_at\": \"2025-06-04T07:06:43.964382\",\n  \"updated_at\": \"2025-06-04T07:06:43.964388\",\n  \"title\": \"训练记录 - 2025-06-04 cdy15s\",\n  \"content\": \"15s\",\n  \"image_urls\": null,\n  \"related_workout_id\": 18,\n  \"id\": 7,\n  \"user_id\": 2,\n  \"like_count\": 0,\n  \"comment_count\": 0,\n  \"share_count\": 0,\n  \"status\": \"ACTIVE\",\n  \"reported_count\": 0,\n  \"user\": {\n    \"id\": 2,\n    \"nickname\": \"微信用户\",\n    \"avatar_url\": \"https://example.com/default-avatar.jpg\",\n    \"gender\": 0,\n    \"age\": null,\n    \"weight\": null,\n    \"height\": null,\n    \"activity_level\": 3,\n    \"bmi\": null,\n    \"tedd\": null,\n    \"completed\": false,\n    \"country\": null,\n    \"province\": null,\n    \"city\": null,\n    \"experience_level\": null,\n    \"fitness_goal\": null,\n    \"health_conditions\": null,\n    \"allergies\": null,\n    \"created_at\": null,\n    \"birthday\": null\n  },\n  \"is_liked_by_current_user\": false,\n  \"related_workout\": {\n    \"id\": 18,\n    \"name\": \"训练记录 - 2025-06-04 cdy15s\",\n    \"title\": \"训练记录 - 2025-06-04 cdy15s\",\n    \"date\": \"2025-06-04T07:06:43.910137\"\n  },\n  \"related_workout_detail\": {\n    \"id\": 67,\n    \"name\": \"训练记录 - 2025-06-04 cdy15s\",\n    \"training_plan_id\": 1,\n    \"day_of_week\": null,\n    \"day_number\": 1,\n    \"description\": null,\n    \"estimated_duration\": null,\n    \"scheduled_date\": null,\n    \"status\": \"completed\",\n    \"actual_duration\": 0,\n    \"net_duration\": null,\n    \"start_time\": \"2025-06-04T07:06:43.893097+08:00\",\n    \"end_time\": \"2025-06-04T07:06:43.893103+08:00\",\n    \"last_status_change\": null,\n    \"created_at\": \"2025-06-04T15:06:43.891842+08:00\",\n    \"updated_at\": \"2025-06-04T15:06:43.891842+08:00\",\n    \"workout_exercises\": [\n      {\n        \"id\": 257,\n        \"workout_id\": 67,\n        \"exercise_id\": 252,\n        \"exercise_name\": \"史密斯反握推举\",\n        \"exercise_image\": \"/data/exercises/images/Smith-Reverse-grip-Press9cbc4d7d0410.png\",\n        \"exercise_description\": null,\n        \"video_url\": \"\",\n        \"sets\": 1,\n        \"reps\": \"12\",\n        \"rest_seconds\": 60,\n        \"order\": 1,\n        \"notes\": null,\n        \"exercise_type\": \"weight_reps\",\n        \"superset_group\": null,\n        \"weight\": null,\n        \"set_records\": [\n          {\n            \"id\": 769,\n            \"workout_exercise_id\": 257,\n            \"set_number\": 1,\n            \"set_type\": \"normal\",\n            \"weight\": 20,\n            \"reps\": 12,\n            \"completed\": false,\n            \"notes\": \"\",\n            \"created_at\": \"2025-06-04T15:06:43.891842+08:00\",\n            \"updated_at\": \"2025-06-04T15:06:43.891842+08:00\"\n          }\n        ]\n      },\n      {\n        \"id\": 258,\n        \"workout_id\": 67,\n        \"exercise_id\": 8,\n        \"exercise_name\": \"下斜卧推\",\n        \"exercise_image\": \"/data/exercises/images/Decline-Bench-Pressa2c4f77ce69e.png\",\n        \"exercise_description\": null,\n        \"video_url\": \"/data/exercises/videos/Decline-Bench-Pressa2c4f77ce69e.mp4\",\n        \"sets\": 4,\n        \"reps\": \"12,12,12,12\",\n        \"rest_seconds\": 60,\n        \"order\": 2,\n        \"notes\": null,\n        \"exercise_type\": \"weight_reps\",\n        \"superset_group\": null,\n        \"weight\": null,\n        \"set_records\": [\n          {\n            \"id\": 770,\n            \"workout_exercise_id\": 258,\n            \"set_number\": 1,\n            \"set_type\": \"normal\",\n            \"weight\": 20,\n            \"reps\": 12,\n            \"completed\": false,\n            \"notes\": \"\",\n            \"created_at\": \"2025-06-04T15:06:43.891842+08:00\",\n            \"updated_at\": \"2025-06-04T15:06:43.891842+08:00\"\n          },\n          {\n            \"id\": 771,\n            \"workout_exercise_id\": 258,\n            \"set_number\": 2,\n            \"set_type\": \"normal\",\n            \"weight\": 20,\n            \"reps\": 12,\n            \"completed\": false,\n            \"notes\": \"\",\n            \"created_at\": \"2025-06-04T15:06:43.891842+08:00\",\n            \"updated_at\": \"2025-06-04T15:06:43.891842+08:00\"\n          },\n          {\n            \"id\": 772,\n            \"workout_exercise_id\": 258,\n            \"set_number\": 3,\n            \"set_type\": \"normal\",\n            \"weight\": 20,\n            \"reps\": 12,\n            \"completed\": false,\n            \"notes\": \"\",\n            \"created_at\": \"2025-06-04T15:06:43.891842+08:00\",\n            \"updated_at\": \"2025-06-04T15:06:43.891842+08:00\"\n          },\n          {\n            \"id\": 773,\n            \"workout_exercise_id\": 258,\n            \"set_number\": 4,\n            \"set_type\": \"normal\",\n            \"weight\": 20,\n            \"reps\": 12,\n            \"completed\": false,\n            \"notes\": \"\",\n            \"created_at\": \"2025-06-04T15:06:43.891842+08:00\",\n            \"updated_at\": \"2025-06-04T15:06:43.891842+08:00\"\n          }\n        ]\n      }\n    ]\n  },\n  \"images\": [],\n  \"comments_summary\": [],\n  \"tags\": [\n    \"训练记录\",\n    \"胸部\"\n  ],\n  \"view_count\": 0\n}\n```\n\n---\n数据文件: /Users/<USER>/Desktop/shihe/sciencefit/description/hevy-fitness-app/memory-bank/api/real-feed-data-sample.json\n"